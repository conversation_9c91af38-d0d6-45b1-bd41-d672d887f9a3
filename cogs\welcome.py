import discord
from discord.ext import commands
from .utils.logging import log_member_leave

WELCOME_CHANNEL_ID = 1389658665119912047
RULES_CHANNEL_ID = 1389660394355949709
INFO_CHANNEL_ID = 1389658868459634749
ROLE_REQUEST_CHANNEL_ID = 1389659025980915742
APPLY_TO_JOIN_CHANNEL_ID = 1389659100802973718
AUTOROLE_ID = 1389657746986500157

class Welcome(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_member_join(self, member):
        guild = member.guild
        # Assign autorole
        role = guild.get_role(AUTOROLE_ID)
        if role is not None:
            try:
                await member.add_roles(role, reason="Auto role on join")
            except Exception:
                pass
        channel = guild.get_channel(WELCOME_CHANNEL_ID)
        if channel is not None:
            embed = discord.Embed(
                title="Welcome to Aegis Nox!",
                description=(
                    f"Welcome {member.mention} to The Aegis Nox Discord Server, Thank you for joining us, you are our {guild.member_count} member! "
                    f"Please check out the <#{RULES_CHANNEL_ID}> & <#{INFO_CHANNEL_ID}> channels!\n"
                    f"If you joined because you're looking to join the Org, Please go to the <#{APPLY_TO_JOIN_CHANNEL_ID}> channel.\n"
                    f"If you are an Affiliate, or other Organization looking to do events or anything else with Aegis Nox, Please go to the <#{ROLE_REQUEST_CHANNEL_ID}> Channel."
                ),
                color=0x000000
            )
            file = discord.File('aegisnoxbanner.png', filename='aegisnoxbanner.png')
            embed.set_image(url="attachment://aegisnoxbanner.png")
            await channel.send(embed=embed, file=file)

    @commands.Cog.listener()
    async def on_member_remove(self, member):
        """Log when a member leaves the server"""
        await log_member_leave(self.bot, member)

async def setup(bot):
    await bot.add_cog(Welcome(bot))