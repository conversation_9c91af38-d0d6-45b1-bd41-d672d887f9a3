2025-07-15 05:11:18,220 [INFO] Loaded cog: applytoaegis.py
2025-07-15 05:11:18,222 [INFO] Loaded cog: embedgen.py
2025-07-15 05:11:18,232 [INFO] Loaded cog: fleetdata.py
2025-07-15 05:11:18,239 [INFO] Loaded cog: giveaway.py
2025-07-15 05:11:18,248 [INFO] Loaded cog: moderation.py
2025-07-15 05:11:18,251 [INFO] Loaded cog: rolerequest.py
2025-07-15 05:11:18,253 [INFO] Loaded cog: rosters.py
2025-07-15 05:11:18,260 [INFO] Loaded cog: shipgame.py
2025-07-15 05:11:18,263 [INFO] Loaded cog: storagetracker.py
2025-07-15 05:11:29,307 [INFO] UEX cache refreshed successfully
2025-07-15 05:11:29,307 [INFO] Loaded cog: uex_trade.py
2025-07-15 05:11:29,328 [INFO] Loaded cog: voice.py
2025-07-15 05:11:29,333 [INFO] Loaded cog: voicetracker.py
2025-07-15 05:11:29,337 [INFO] Loaded cog: welcome.py
2025-07-15 05:11:29,337 [INFO] logging in using static token
2025-07-15 05:11:30,113 [INFO] Shard ID None has connected to Gateway (Session ID: 0ce41573a2e7b715b35cf55764ab014a).
2025-07-15 05:11:32,157 [INFO] Logged in as Aegis Nox Bot
2025-07-15 05:11:32,365 [INFO] Synced 32 slash commands globally.
2025-07-15 05:11:32,365 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'serverinfo', 'orginfo', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'testinvestment', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-15 05:11:49,840 [ERROR] Ignoring exception in command 'serverinfo'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\moderation.py", line 471, in server_info
    timestamp=datetime.now(datetime.timezone.utc)
                           ^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timezone'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'serverinfo' raised an exception: AttributeError: type object 'datetime.datetime' has no attribute 'timezone'
2025-07-15 05:16:45,699 [INFO] Loaded cog: applytoaegis.py
2025-07-15 05:16:45,701 [INFO] Loaded cog: embedgen.py
2025-07-15 05:16:45,703 [INFO] Loaded cog: fleetdata.py
2025-07-15 05:16:45,705 [INFO] Loaded cog: giveaway.py
2025-07-15 05:16:45,715 [INFO] Loaded cog: moderation.py
2025-07-15 05:16:45,717 [INFO] Loaded cog: rolerequest.py
2025-07-15 05:16:45,718 [INFO] Loaded cog: rosters.py
2025-07-15 05:16:45,720 [INFO] Loaded cog: shipgame.py
2025-07-15 05:16:45,721 [INFO] Loaded cog: storagetracker.py
2025-07-15 05:16:56,681 [INFO] UEX cache refreshed successfully
2025-07-15 05:16:56,681 [INFO] Loaded cog: uex_trade.py
2025-07-15 05:16:56,683 [INFO] Loaded cog: voice.py
2025-07-15 05:16:56,686 [INFO] Loaded cog: voicetracker.py
2025-07-15 05:16:56,687 [INFO] Loaded cog: welcome.py
2025-07-15 05:16:56,687 [INFO] logging in using static token
2025-07-15 05:16:57,560 [INFO] Shard ID None has connected to Gateway (Session ID: 55640133031d70f8e0e8735d0ce8966c).
2025-07-15 05:16:59,556 [INFO] Logged in as Aegis Nox Bot
2025-07-15 05:16:59,790 [INFO] Synced 32 slash commands globally.
2025-07-15 05:16:59,790 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'serverinfo', 'orginfo', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'testinvestment', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
