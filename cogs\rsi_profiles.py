import discord
from discord.ext import commands
from discord import app_commands
from discord.ui import Modal, TextInput, View, Button
from datetime import datetime, timezone
from .utils.rsi_profiles import (
    link_rsi_profile, 
    get_rsi_profile, 
    is_valid_rsi_url, 
    extract_citizen_name
)

class RSILinkModal(Modal):
    def __init__(self):
        super().__init__(title="Link RSI Profile")
        
        self.rsi_url = TextInput(
            label="RSI Citizen Dossier Link",
            placeholder="https://robertsspaceindustries.com/en/citizens/YourCitizenName",
            style=discord.TextStyle.short,
            required=True,
            max_length=200
        )
        self.add_item(self.rsi_url)

    async def on_submit(self, interaction: discord.Interaction):
        url = self.rsi_url.value.strip()
        
        # Validate the URL
        if not is_valid_rsi_url(url):
            await interaction.response.send_message(
                "❌ Invalid RSI profile URL. Please make sure you're using a valid RSI citizen dossier link.\n"
                "Example: `https://robertsspaceindustries.com/en/citizens/YourCitizenName`",
                ephemeral=True
            )
            return
        
        # Link the profile
        user_id = str(interaction.user.id)
        success = link_rsi_profile(user_id, url)
        
        if success:
            citizen_name = extract_citizen_name(url)
            embed = discord.Embed(
                title="✅ RSI Profile Linked Successfully!",
                description=f"Your RSI profile has been linked to your Discord account.",
                color=discord.Color.green(),
                timestamp=datetime.now(timezone.utc)
            )
            
            embed.add_field(
                name="Citizen Name",
                value=citizen_name or "Unknown",
                inline=True
            )
            
            embed.add_field(
                name="Profile URL",
                value=f"[View Profile]({url})",
                inline=True
            )
            
            embed.set_footer(text=f"Linked by {interaction.user.display_name}")
            
            await interaction.response.send_message(embed=embed, ephemeral=True)
        else:
            await interaction.response.send_message(
                "❌ Failed to link your RSI profile. Please try again.",
                ephemeral=True
            )

class RSILinkView(View):
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(label="Link RSI Profile", style=discord.ButtonStyle.primary, emoji="🔗")
    async def link_button(self, interaction: discord.Interaction, button: Button):
        modal = RSILinkModal()
        await interaction.response.send_modal(modal)

class RSIProfiles(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @app_commands.command(name="linkrsi", description="Link your RSI profile to your Discord account")
    async def link_rsi(self, interaction: discord.Interaction):
        # Check if user already has a linked profile
        user_id = str(interaction.user.id)
        existing_profile = get_rsi_profile(user_id)
        
        embed = discord.Embed(
            title="🔗 Link Your RSI Profile",
            description=(
                "To link your RSI account to Discord, click the **Link** button below and provide your RSI Citizen Dossier link.\n\n"
                "**Example RSI Profile Link:**\n"
                "`https://robertsspaceindustries.com/en/citizens/Gnikss`\n\n"
                "**How to find your RSI profile:**\n"
                "1. Go to [robertsspaceindustries.com](https://robertsspaceindustries.com)\n"
                "2. Log in to your account\n"
                "3. Click on your profile/avatar\n"
                "4. Copy the URL from your citizen dossier page"
            ),
            color=discord.Color.blue(),
            timestamp=datetime.now(timezone.utc)
        )
        
        if existing_profile:
            citizen_name = extract_citizen_name(existing_profile)
            embed.add_field(
                name="Current Linked Profile",
                value=f"**Citizen:** {citizen_name or 'Unknown'}\n[View Profile]({existing_profile})",
                inline=False
            )
            embed.set_footer(text="Click the button below to update your linked profile")
        else:
            embed.set_footer(text="You don't have a linked RSI profile yet")
        
        view = RSILinkView()
        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

    @app_commands.command(name="userinfo", description="Display information about a user")
    @app_commands.describe(user="The user to get information about")
    async def user_info(self, interaction: discord.Interaction, user: discord.Member):
        # Get RSI profile if linked
        user_id = str(user.id)
        rsi_profile = get_rsi_profile(user_id)
        
        # Create embed
        embed = discord.Embed(
            title=f"👤 User Information - {user.display_name}",
            color=discord.Color.blue(),
            timestamp=datetime.now(timezone.utc)
        )
        
        # Set user avatar as thumbnail
        if user.avatar:
            embed.set_thumbnail(url=user.avatar.url)
        elif user.default_avatar:
            embed.set_thumbnail(url=user.default_avatar.url)
        
        # Basic user info
        embed.add_field(
            name="📋 Basic Information",
            value=(
                f"**Username:** {user.name}#{user.discriminator}\n"
                f"**Display Name:** {user.display_name}\n"
                f"**User ID:** `{user.id}`\n"
                f"**Bot:** {'Yes' if user.bot else 'No'}"
            ),
            inline=False
        )
        
        # Account dates
        account_age = datetime.now(timezone.utc) - user.created_at
        join_age = datetime.now(timezone.utc) - user.joined_at if user.joined_at else None
        
        date_info = f"**Account Created:** {user.created_at.strftime('%B %d, %Y at %H:%M UTC')}\n({account_age.days} days ago)\n\n"
        
        if user.joined_at:
            date_info += f"**Joined Server:** {user.joined_at.strftime('%B %d, %Y at %H:%M UTC')}\n({join_age.days} days ago)"
        else:
            date_info += "**Joined Server:** Unknown"
        
        embed.add_field(
            name="📅 Dates",
            value=date_info,
            inline=True
        )
        
        # RSI Profile
        if rsi_profile:
            citizen_name = extract_citizen_name(rsi_profile)
            rsi_info = f"**Citizen Name:** {citizen_name or 'Unknown'}\n[View RSI Profile]({rsi_profile})"
        else:
            rsi_info = "No RSI profile linked"
        
        embed.add_field(
            name="🚀 RSI Profile",
            value=rsi_info,
            inline=True
        )
        
        # Roles (limit to prevent embed from being too long)
        roles = [role.mention for role in user.roles if role != interaction.guild.default_role]
        if roles:
            roles_text = ", ".join(roles[:20])  # Limit to 20 roles
            if len(user.roles) > 21:  # 20 roles + @everyone
                roles_text += f"\n... and {len(user.roles) - 21} more roles"
        else:
            roles_text = "No roles"
        
        if len(roles_text) > 1024:  # Discord field value limit
            roles_text = roles_text[:1020] + "..."
        
        embed.add_field(
            name=f"🎭 Roles ({len(roles)})",
            value=roles_text,
            inline=False
        )
        
        # Additional info
        status_emoji = {
            discord.Status.online: "🟢",
            discord.Status.idle: "🟡", 
            discord.Status.dnd: "🔴",
            discord.Status.offline: "⚫"
        }
        
        embed.add_field(
            name="📊 Status",
            value=(
                f"**Status:** {status_emoji.get(user.status, '❓')} {user.status.name.title()}\n"
                f"**Mobile:** {'Yes' if user.is_on_mobile() else 'No'}"
            ),
            inline=True
        )
        
        embed.set_footer(text=f"Requested by {interaction.user.display_name}")
        
        await interaction.response.send_message(embed=embed)

async def setup(bot):
    await bot.add_cog(RSIProfiles(bot))
