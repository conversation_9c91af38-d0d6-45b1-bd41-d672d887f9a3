import discord
from datetime import datetime, timezone

LOG_CHANNEL_ID = 1389705853095120942

async def log_moderation_action(bot, action_type: str, moderator: discord.Member, target: discord.Member = None, reason: str = None, **kwargs):
    """
    Log a moderation action to the logging channel
    """
    log_channel = bot.get_channel(LOG_CHANNEL_ID)
    if not log_channel:
        return

    embed = discord.Embed(
        title=f"Moderation Action: {action_type}",
        color=discord.Color.red(),
        timestamp=datetime.now(timezone.utc)
    )

    # Handle case where target might be None (e.g., bulk message deletion)
    if target:
        embed.add_field(name="Target", value=f"{target.mention} ({target.name}#{target.discriminator})\nID: {target.id}", inline=False)
        embed.set_footer(text=f"User ID: {target.id}")
    else:
        embed.add_field(name="Target", value="No specific target (bulk action)", inline=False)
        embed.set_footer(text="Bulk moderation action")

    embed.add_field(name="Moderator", value=f"{moderator.mention} ({moderator.name}#{moderator.discriminator})\nID: {moderator.id}", inline=False)

    if reason:
        embed.add_field(name="Reason", value=reason, inline=False)

    # Add any additional fields from kwargs
    for key, value in kwargs.items():
        if value:
            embed.add_field(name=key.replace('_', ' ').title(), value=str(value), inline=False)

    await log_channel.send(embed=embed)

async def log_role_change(bot, action_type: str, member: discord.Member, role: discord.Role, moderator: discord.Member = None, reason: str = None):
    """
    Log a role change to the logging channel
    """
    log_channel = bot.get_channel(LOG_CHANNEL_ID)
    if not log_channel:
        return

    color = discord.Color.green() if action_type == "Role Added" else discord.Color.red()
    
    embed = discord.Embed(
        title=f"{action_type}",
        color=color,
        timestamp=datetime.now(timezone.utc)
    )

    embed.add_field(name="Member", value=f"{member.mention} ({member.name}#{member.discriminator})\nID: {member.id}", inline=False)
    embed.add_field(name="Role", value=f"{role.mention} ({role.name})\nID: {role.id}", inline=False)
    
    if moderator:
        embed.add_field(name="Moderator", value=f"{moderator.mention} ({moderator.name}#{moderator.discriminator})\nID: {moderator.id}", inline=False)
    
    if reason:
        embed.add_field(name="Reason", value=reason, inline=False)

    # Add current roles
    roles = [role.mention for role in member.roles if role != member.guild.default_role]
    roles_str = ", ".join(roles) if roles else "None"
    embed.add_field(name="Current Roles", value=roles_str, inline=False)

    embed.set_footer(text=f"User ID: {member.id}")
    
    await log_channel.send(embed=embed)

async def log_promotion(bot, member: discord.Member, old_rank: discord.Role, new_rank: discord.Role, moderator: discord.Member):
    """
    Log a promotion to the logging channel
    """
    log_channel = bot.get_channel(LOG_CHANNEL_ID)
    if not log_channel:
        return

    embed = discord.Embed(
        title="Member Promoted",
        color=discord.Color.green(),
        timestamp=datetime.now(timezone.utc)
    )

    embed.add_field(name="Member", value=f"{member.mention} ({member.name}#{member.discriminator})\nID: {member.id}", inline=False)
    embed.add_field(name="Old Rank", value=f"{old_rank.mention if old_rank else 'None'}", inline=True)
    embed.add_field(name="New Rank", value=f"{new_rank.mention}", inline=True)
    embed.add_field(name="Moderator", value=f"{moderator.mention} ({moderator.name}#{moderator.discriminator})\nID: {moderator.id}", inline=False)

    # Add current roles
    roles = [role.mention for role in member.roles if role != member.guild.default_role]
    roles_str = ", ".join(roles) if roles else "None"
    embed.add_field(name="Current Roles", value=roles_str, inline=False)

    embed.set_footer(text=f"User ID: {member.id}")
    
    await log_channel.send(embed=embed)

async def log_demotion(bot, member: discord.Member, old_rank: discord.Role, new_rank: discord.Role, moderator: discord.Member):
    """
    Log a demotion to the logging channel
    """
    log_channel = bot.get_channel(LOG_CHANNEL_ID)
    if not log_channel:
        return

    embed = discord.Embed(
        title="Member Demoted",
        color=discord.Color.orange(),
        timestamp=datetime.now(timezone.utc)
    )

    embed.add_field(name="Member", value=f"{member.mention} ({member.name}#{member.discriminator})\nID: {member.id}", inline=False)
    embed.add_field(name="Old Rank", value=f"{old_rank.mention if old_rank else 'None'}", inline=True)
    embed.add_field(name="New Rank", value=f"{new_rank.mention if new_rank else 'None'}", inline=True)
    embed.add_field(name="Moderator", value=f"{moderator.mention} ({moderator.name}#{moderator.discriminator})\nID: {moderator.id}", inline=False)

    # Add current roles
    roles = [role.mention for role in member.roles if role != member.guild.default_role]
    roles_str = ", ".join(roles) if roles else "None"
    embed.add_field(name="Current Roles", value=roles_str, inline=False)

    embed.set_footer(text=f"User ID: {member.id}")

    await log_channel.send(embed=embed)

async def log_member_leave(bot, member: discord.Member):
    """
    Log when a member leaves the server
    """
    log_channel = bot.get_channel(LOG_CHANNEL_ID)
    if not log_channel:
        return

    embed = discord.Embed(
        title="Member Left Server",
        color=discord.Color.red(),
        timestamp=datetime.now(timezone.utc)
    )

    # Member information
    embed.add_field(
        name="Member",
        value=f"{member.mention} ({member.name}#{member.discriminator})\nID: {member.id}",
        inline=False
    )

    # Account creation date
    account_age = datetime.now(timezone.utc) - member.created_at
    embed.add_field(
        name="Account Created",
        value=f"{member.created_at.strftime('%B %d, %Y at %H:%M UTC')}\n({account_age.days} days ago)",
        inline=True
    )

    # Server join date (if available)
    if member.joined_at:
        server_time = datetime.now(timezone.utc) - member.joined_at
        embed.add_field(
            name="Joined Server",
            value=f"{member.joined_at.strftime('%B %d, %Y at %H:%M UTC')}\n({server_time.days} days ago)",
            inline=True
        )

    # Roles they had
    roles = [role.mention for role in member.roles if role != member.guild.default_role]
    roles_str = ", ".join(roles) if roles else "None"
    if len(roles_str) > 1024:  # Discord field value limit
        roles_str = roles_str[:1020] + "..."
    embed.add_field(name="Roles", value=roles_str, inline=False)

    # Set member avatar as thumbnail if available
    if member.avatar:
        embed.set_thumbnail(url=member.avatar.url)
    elif member.default_avatar:
        embed.set_thumbnail(url=member.default_avatar.url)

    embed.set_footer(text=f"User ID: {member.id}")

    await log_channel.send(embed=embed)