import discord
from discord.ext import commands, tasks
from discord import app_commands
from discord.ui import <PERSON><PERSON>, View
import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, List

class LeaderboardView(View):
    def __init__(self, entries: List[tuple], page_size: int = 10, formatter=None):
        super().__init__(timeout=180)  # 3 minute timeout
        self.entries = entries
        self.page_size = page_size
        self.current_page = 0
        self.formatter = formatter or (lambda x: x)
        self.max_pages = (len(entries) - 1) // page_size + 1
        
        # Add buttons
        self.add_item(Button(label="◀", custom_id="prev", style=discord.ButtonStyle.primary, disabled=True))
        self.add_item(Button(label="▶", custom_id="next", style=discord.ButtonStyle.primary, disabled=self.max_pages <= 1))

    async def update_buttons(self, interaction: discord.Interaction):
        # Update button states
        prev_button = self.children[0]
        next_button = self.children[1]
        
        prev_button.disabled = self.current_page == 0
        next_button.disabled = self.current_page >= self.max_pages - 1
        
        # Create embed for current page
        start_idx = self.current_page * self.page_size
        page_entries = self.entries[start_idx:start_idx + self.page_size]
        
        description = []
        for i, entry in enumerate(page_entries, start=start_idx + 1):
            description.append(self.formatter(i, entry))

        embed = discord.Embed(
            title=f"Leaderboard (Page {self.current_page + 1}/{self.max_pages})",
            description="\n".join(description) if description else "No entries found.",
            color=discord.Color.from_rgb(0, 0, 0)  # Black color [[memory:2991198]]
        )
        
        await interaction.response.edit_message(embed=embed, view=self)

    async def on_button_click(self, interaction: discord.Interaction, button: Button):
        if button.custom_id == "prev" and self.current_page > 0:
            self.current_page -= 1
        elif button.custom_id == "next" and self.current_page < self.max_pages - 1:
            self.current_page += 1
            
        await self.update_buttons(interaction)

class VoiceTracker(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.voice_data_file = 'data/voice_activity.json'
        self.message_data_file = 'data/message_activity.json'
        self.voice_times: Dict[str, dict] = {}
        self.message_counts: Dict[str, int] = {}
        self.voice_states: Dict[str, float] = {}  # Stores join timestamps
        self.load_data()
        self.check_inactivity.start()

    def load_data(self):
        # Load voice activity data
        if os.path.exists(self.voice_data_file):
            try:
                with open(self.voice_data_file, 'r') as f:
                    self.voice_times = json.load(f)
                    # Convert warnings_sent lists back to sets
                    for user_data in self.voice_times.values():
                        if "warnings_sent" in user_data:
                            user_data["warnings_sent"] = set(user_data["warnings_sent"])
            except json.JSONDecodeError as e:
                print(f"Error loading voice activity data: {e}")
                print("Creating backup and starting with empty data...")
                # Create backup of corrupted file
                import shutil
                backup_file = f"{self.voice_data_file}.backup"
                shutil.copy2(self.voice_data_file, backup_file)
                print(f"Corrupted file backed up to: {backup_file}")
                # Start with empty data
                self.voice_times = {}

        # Load message activity data
        if os.path.exists(self.message_data_file):
            try:
                with open(self.message_data_file, 'r') as f:
                    self.message_counts = json.load(f)
            except json.JSONDecodeError as e:
                print(f"Error loading message activity data: {e}")
                print("Starting with empty message data...")
                self.message_counts = {}

    def save_data(self):
        # Convert sets to lists for JSON serialization
        save_data = {}
        for user_id, data in self.voice_times.items():
            save_data[user_id] = data.copy()  # Create a copy to avoid modifying original
            if "warnings_sent" in save_data[user_id]:
                save_data[user_id]["warnings_sent"] = list(save_data[user_id]["warnings_sent"])

        # Save voice activity data
        os.makedirs(os.path.dirname(self.voice_data_file), exist_ok=True)
        with open(self.voice_data_file, 'w') as f:
            json.dump(save_data, f, indent=4)
        
        # Save message activity data
        os.makedirs(os.path.dirname(self.message_data_file), exist_ok=True)
        with open(self.message_data_file, 'w') as f:
            json.dump(self.message_counts, f, indent=4)

    def format_duration(self, seconds: float) -> str:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        return f"{hours} hours, {minutes} minutes"

    def initialize_user_data(self, user_id: str) -> None:
        """Initialize data structures for a new user"""
        # Set initial timestamp to 30 seconds ago to start the progression
        past_time = time.time() - 30  # Start with 30 seconds of inactivity
        
        if user_id not in self.voice_times:
            self.voice_times[user_id] = {
                "total_time": 0,
                "last_activity": past_time,
                "last_message": past_time,
                "warnings_sent": set()
            }
        if user_id not in self.message_counts:
            self.message_counts[user_id] = 0
        self.save_data()

    async def remove_inactive_role(self, member: discord.Member) -> None:
        """Remove inactive role and reset warnings if user becomes active"""
        # Get the inactive role
        inactive_role = member.guild.get_role(1393309592166858793)
        if inactive_role and inactive_role in member.roles:
            await member.remove_roles(inactive_role)
            
        # Reset warnings
        user_id = str(member.id)
        if user_id in self.voice_times:
            if "warnings_sent" not in self.voice_times[user_id]:
                self.voice_times[user_id]["warnings_sent"] = set()
            else:
                self.voice_times[user_id]["warnings_sent"].clear()  # Clear instead of creating new set
            self.save_data()

    @commands.Cog.listener()
    async def on_voice_state_update(self, member: discord.Member, before: discord.VoiceState, after: discord.VoiceState):
        user_id = str(member.id)
        current_time = time.time()
        
        # Initialize user data if not exists
        if user_id not in self.voice_times:
            self.voice_times[user_id] = {
                "total_time": 0,
                "last_activity": current_time,
                "last_update": current_time,
                "warnings_sent": set()
            }

        # User joined a voice channel
        if before.channel is None and after.channel is not None:
            self.voice_states[user_id] = current_time
            self.voice_times[user_id]["last_activity"] = current_time
            self.voice_times[user_id]["last_update"] = current_time
            # Remove inactive role when user joins voice
            await self.remove_inactive_role(member)

        # User left a voice channel
        elif before.channel is not None and after.channel is None:
            if user_id in self.voice_states:
                join_time = self.voice_states[user_id]
                time_spent = current_time - join_time
                self.voice_times[user_id]["total_time"] += time_spent
                self.voice_times[user_id]["last_update"] = current_time
                del self.voice_states[user_id]
                self.save_data()

    @commands.Cog.listener()
    async def on_message(self, message: discord.Message):
        if message.author.bot:
            return

        user_id = str(message.author.id)
        
        # Update message count
        self.message_counts[user_id] = self.message_counts.get(user_id, 0) + 1
        
        # Update last activity time in voice_times
        if user_id not in self.voice_times:
            self.voice_times[user_id] = {
                "total_time": 0,
                "last_activity": time.time(),
                "warnings_sent": set()
            }
        self.voice_times[user_id]["last_message"] = time.time()
        
        # Convert User to Member before removing inactive role
        if message.guild:  # Only proceed if message is from a guild
            try:
                member = message.guild.get_member(message.author.id) or await message.guild.fetch_member(message.author.id)
                if member:
                    await self.remove_inactive_role(member)
            except discord.HTTPException:
                pass  # Member not found or other Discord API error
        
        self.save_data()

    @commands.Cog.listener()
    async def on_member_join(self, member: discord.Member):
        """Initialize data tracking when a new member joins"""
        # For new members, we'll set current time since they just joined
        user_id = str(member.id)
        if user_id not in self.voice_times:
            self.voice_times[user_id] = {
                "total_time": 0,
                "last_activity": time.time(),
                "last_message": time.time()
            }
        if user_id not in self.message_counts:
            self.message_counts[user_id] = 0
        self.save_data()

    @app_commands.command(name="voicetime", description="Check a user's voice channel time")
    async def voice_time(self, interaction: discord.Interaction, user: Optional[discord.Member] = None):
        target = user or interaction.user
        user_id = str(target.id)
        
        if user_id not in self.voice_times:
            await interaction.response.send_message(f"{target.display_name} has no recorded voice activity.")
            return

        total_time = self.voice_times[user_id]["total_time"]
        formatted_time = self.format_duration(total_time)
        
        embed = discord.Embed(
            title="Voice Activity Time",
            description=f"{target.display_name}'s total voice time: {formatted_time}",
            color=discord.Color.from_rgb(0, 0, 0)  # Black color [[memory:2991198]]
        )
        await interaction.response.send_message(embed=embed)

    @app_commands.command(name="voicelb", description="Show voice time leaderboard")
    async def voice_leaderboard(self, interaction: discord.Interaction):
        sorted_times = sorted(
            self.voice_times.items(),
            key=lambda x: x[1]["total_time"],
            reverse=True
        )

        async def format_voice_entry(idx: int, entry: tuple) -> str:
            user_id, data = entry
            user = self.bot.get_user(int(user_id))
            if user:
                formatted_time = self.format_duration(data["total_time"])
                return f"{idx}. {user.display_name}: {formatted_time}"
            return None

        # Create view with pagination
        view = LeaderboardView(
            sorted_times,
            formatter=lambda i, x: f"{i}. {self.bot.get_user(int(x[0])).display_name}: {self.format_duration(x[1]['total_time'])}" if self.bot.get_user(int(x[0])) else None
        )
        
        # Create initial embed
        description = []
        for i, (user_id, data) in enumerate(sorted_times[:10], 1):
            user = self.bot.get_user(int(user_id))
            if user:
                formatted_time = self.format_duration(data["total_time"])
                description.append(f"{i}. {user.display_name}: {formatted_time}")

        embed = discord.Embed(
            title=f"Voice Time Leaderboard (Page 1/{view.max_pages})",
            description="\n".join(description) if description else "No voice activity recorded.",
            color=discord.Color.from_rgb(0, 0, 0)  # Black color [[memory:2991198]]
        )
        
        message = await interaction.response.send_message(embed=embed, view=view)
        
        # Set up button callbacks
        async def button_callback(interaction: discord.Interaction):
            button = discord.utils.get(view.children, custom_id=interaction.data["custom_id"])
            if button:
                await view.on_button_click(interaction, button)
        
        view.children[0].callback = button_callback  # prev button
        view.children[1].callback = button_callback  # next button

    @app_commands.command(name="msgcount", description="Check a user's message count")
    async def message_count(self, interaction: discord.Interaction, user: Optional[discord.Member] = None):
        target = user or interaction.user
        user_id = str(target.id)
        count = self.message_counts.get(user_id, 0)

        embed = discord.Embed(
            title="Message Count",
            description=f"{target.display_name} has sent {count} messages",
            color=discord.Color.from_rgb(0, 0, 0)  # Black color [[memory:2991198]]
        )
        await interaction.response.send_message(embed=embed)

    @app_commands.command(name="msglb", description="Show message count leaderboard")
    async def message_leaderboard(self, interaction: discord.Interaction):
        sorted_counts = sorted(
            self.message_counts.items(),
            key=lambda x: x[1],
            reverse=True
        )

        # Create view with pagination
        view = LeaderboardView(
            sorted_counts,
            formatter=lambda i, x: f"{i}. {self.bot.get_user(int(x[0])).display_name}: {x[1]} messages" if self.bot.get_user(int(x[0])) else None
        )
        
        # Create initial embed
        description = []
        for i, (user_id, count) in enumerate(sorted_counts[:10], 1):
            user = self.bot.get_user(int(user_id))
            if user:
                description.append(f"{i}. {user.display_name}: {count} messages")

        embed = discord.Embed(
            title=f"Message Count Leaderboard (Page 1/{view.max_pages})",
            description="\n".join(description) if description else "No messages recorded.",
            color=discord.Color.from_rgb(0, 0, 0)  # Black color [[memory:2991198]]
        )
        
        message = await interaction.response.send_message(embed=embed, view=view)
        
        # Set up button callbacks
        async def button_callback(interaction: discord.Interaction):
            button = discord.utils.get(view.children, custom_id=interaction.data["custom_id"])
            if button:
                await view.on_button_click(interaction, button)
        
        view.children[0].callback = button_callback  # prev button
        view.children[1].callback = button_callback  # next button

    @app_commands.command(name="populatetracking", description="Add all current server members to tracking data")
    @app_commands.checks.has_permissions(administrator=True)
    async def populate_tracking(self, interaction: discord.Interaction):
        """Add all current server members to the tracking data"""
        await interaction.response.defer()
        
        count = 0
        for member in interaction.guild.members:
            if not member.bot:  # Skip bots
                user_id = str(member.id)
                # Check member's status and activity
                is_active = (
                    member.voice is not None  # In voice channel
                    or member.status != discord.Status.offline  # Online status
                )
                
                if is_active:
                    # If member is currently active, set current time
                    if user_id not in self.voice_times:
                        self.voice_times[user_id] = {
                            "total_time": 0,
                            "last_activity": time.time(),
                            "last_message": time.time()
                        }
                else:
                    # If member is not active, set to 30 seconds ago
                    if user_id not in self.voice_times:
                        past_time = time.time() - 30  # Start with 30 seconds of inactivity
                        self.voice_times[user_id] = {
                            "total_time": 0,
                            "last_activity": past_time,
                            "last_message": past_time
                        }
                
                if user_id not in self.message_counts:
                    self.message_counts[user_id] = 0
                count += 1
        
        self.save_data()
        
        embed = discord.Embed(
            title="Tracking Data Population",
            description=f"Successfully added {count} members to tracking data. Inactivity warnings will begin in 30 seconds.",
            color=discord.Color.from_rgb(0, 0, 0)  # Black color [[memory:2991198]]
        )
        await interaction.followup.send(embed=embed)

    async def send_inactivity_dm(self, member: discord.Member, days: int) -> None:
        """Send a DM to inactive users"""
        try:
            embed = discord.Embed(
                title="Inactivity Notice",
                color=discord.Color.from_rgb(0, 0, 0)  # Black color [[memory:2991198]]
            )
            
            if days == 7:
                embed.description = (
                    "Hey there! We've noticed you haven't been in voice chat for 7 days. "
                    "We miss having you around! Would love to see you hop back in and play some Star Citizen with Aegis Nox again. "
                    "Hope to see you soon! 🚀 "
                    "Join us again here in our Discord! https://discord.gg/aegisnox"
                )
            elif days == 14:
                embed.description = (
                    "It's been 14 days since we last saw you in voice chat. "
                    "Due to inactivity, you'll be moved to the Reserves role if your a member of Aegis Nox, but don't worry! "
                    "You can always come back and play Star Citizen with us anytime. "
                    "We hope to see you back in action soon! 🎮 "
                    "Join us again here in our Discord! https://discord.gg/aegisnox"
                )
            elif days == 21:
                embed.description = (
                    "We haven't seen you in voice chat or text channels for 21 days. "
                    "You'll be marked as inactive, but you're always welcome to come back! "
                    "We'd love to have you join us again for some Star Citizen adventures. "
                    "Just hop in whenever you're ready to return! ⭐ "
                    "Join us again here in our Discord! https://discord.gg/aegisnox"
                )
            
            await member.send(embed=embed)
        except discord.HTTPException:
            # Failed to send DM (user might have DMs disabled)
            pass

    @tasks.loop(hours=1)  # Check every hour
    async def check_inactivity(self):
        current_time = time.time()
        inactivity_channel = self.bot.get_channel(1389716725162971206)
        
        if not inactivity_channel:
            return

        # Get the guild from the channel
        guild = inactivity_channel.guild
        
        # Fetch actual role objects
        founder_role = guild.get_role(1389657163252760636)
        reserves_role = guild.get_role(1393309262624850010)
        aegis_nox_role = guild.get_role(1389657832063897721)
        inactive_role = guild.get_role(1393309592166858793)

        # Check if roles exist
        if not all([founder_role, reserves_role, aegis_nox_role, inactive_role]):
            print("Warning: Some roles could not be found")
            return

        # Update activity time and total time for users currently in voice channels
        for voice_channel in guild.voice_channels:
            for member in voice_channel.members:
                user_id = str(member.id)
                if user_id in self.voice_times:
                    # Calculate time since last update
                    last_update = self.voice_times[user_id].get("last_update", current_time)
                    time_since_update = current_time - last_update
                    
                    # Update total time
                    self.voice_times[user_id]["total_time"] += time_since_update
                    
                    # Update timestamps
                    self.voice_times[user_id]["last_activity"] = current_time
                    self.voice_times[user_id]["last_update"] = current_time
                    
                    # Remove any inactive roles if they're in voice
                    if inactive_role in member.roles:
                        await member.remove_roles(inactive_role)
                    # Reset warnings since they're active
                    self.voice_times[user_id]["warnings_sent"] = set()
                else:
                    # Initialize data for new users
                    self.voice_times[user_id] = {
                        "total_time": 0,
                        "last_activity": current_time,
                        "last_update": current_time,
                        "warnings_sent": set()
                    }
                
        self.save_data()  # Save after updating all active users

        for user_id, data in self.voice_times.items():
            try:
                member = await guild.fetch_member(int(user_id))
                if not member:
                    continue

                # Skip if user is currently in a voice channel
                if any(member in vc.members for vc in guild.voice_channels):
                    continue

                last_voice = data.get("last_activity", 0)
                last_message = data.get("last_message", 0)
                last_activity = max(last_voice, last_message)
                days_inactive = (current_time - last_activity) / (24 * 3600)  # Convert to days

                # Store whether warnings have been sent to avoid duplicate notifications
                warnings_sent = data.get("warnings_sent", set())
                if "warnings_sent" not in data:
                    data["warnings_sent"] = set()

                # 7-day check
                if 7 <= days_inactive < 14 and "7day" not in warnings_sent:
                    await inactivity_channel.send(
                        f"{founder_role.mention} User {member.mention} has not joined a voice channel in 7 days."
                    )
                    await self.send_inactivity_dm(member, 7)
                    data["warnings_sent"].add("7day")
                    self.save_data()

                # 14-day check for Aegis Nox members
                elif 14 <= days_inactive < 21 and aegis_nox_role in member.roles and "14day" not in warnings_sent:
                    await inactivity_channel.send(
                        f"{founder_role.mention} User {member.mention} has been inactive for 14 days. Assigning Reserves role."
                    )
                    await self.send_inactivity_dm(member, 14)
                    await member.add_roles(reserves_role)
                    data["warnings_sent"].add("14day")
                    self.save_data()

                # 21-day check for all users
                elif days_inactive >= 21 and "21day" not in warnings_sent:
                    await inactivity_channel.send(
                        f"User {member.mention} has been inactive for 21 days. Assigning Inactive role."
                    )
                    await self.send_inactivity_dm(member, 21)
                    await member.add_roles(inactive_role)
                    data["warnings_sent"].add("21day")
                    self.save_data()

            except discord.HTTPException:
                continue

    @check_inactivity.before_loop
    async def before_check_inactivity(self):
        await self.bot.wait_until_ready()

    def cog_unload(self):
        self.check_inactivity.cancel()
        self.save_data()

async def setup(bot):
    await bot.add_cog(VoiceTracker(bot)) 