import json
import os
import re
from typing import Optional, Dict, Any

# Create data directory if it doesn't exist
if not os.path.exists('data'):
    os.makedirs('data')

RSI_PROFILES_FILE = 'data/rsi_profiles.json'

def load_rsi_profiles() -> Dict[str, str]:
    """
    Load RSI profiles from JSON file
    Returns a dictionary mapping user IDs to RSI profile URLs
    """
    try:
        with open(RSI_PROFILES_FILE, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def save_rsi_profiles(profiles: Dict[str, str]) -> None:
    """
    Save RSI profiles to JSON file
    """
    with open(RSI_PROFILES_FILE, 'w') as f:
        json.dump(profiles, f, indent=4)

def link_rsi_profile(user_id: str, rsi_url: str) -> bool:
    """
    Link a user's Discord ID to their RSI profile URL
    Returns True if successful, False otherwise
    """
    # Validate RSI URL format
    if not is_valid_rsi_url(rsi_url):
        return False
    
    # Load existing profiles
    profiles = load_rsi_profiles()
    
    # Add or update the user's profile
    profiles[user_id] = rsi_url
    
    # Save profiles
    save_rsi_profiles(profiles)
    
    return True

def unlink_rsi_profile(user_id: str) -> bool:
    """
    Unlink a user's RSI profile
    Returns True if successful, False if the user had no linked profile
    """
    profiles = load_rsi_profiles()
    
    if user_id in profiles:
        del profiles[user_id]
        save_rsi_profiles(profiles)
        return True
    
    return False

def get_rsi_profile(user_id: str) -> Optional[str]:
    """
    Get a user's RSI profile URL
    Returns None if the user has no linked profile
    """
    profiles = load_rsi_profiles()
    return profiles.get(user_id)

def is_valid_rsi_url(url: str) -> bool:
    """
    Validate that a URL is a valid RSI citizen profile URL
    """
    # Basic pattern for RSI citizen profile URLs
    pattern = r'^https?://(?:www\.)?robertsspaceindustries\.com/(?:en/)?citizens/[a-zA-Z0-9_-]+'
    
    return bool(re.match(pattern, url))

def extract_citizen_name(url: str) -> Optional[str]:
    """
    Extract the citizen name from an RSI profile URL
    Returns None if the URL is invalid
    """
    if not is_valid_rsi_url(url):
        return None
    
    # Extract the citizen name from the URL
    match = re.search(r'/citizens/([a-zA-Z0-9_-]+)', url)
    if match:
        return match.group(1)
    
    return None
